import 'package:flutter/material.dart';

class FontTestScreen extends StatelessWidget {
  const FontTestScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('字体测试页面'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: <PERSON>um<PERSON>(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '默认字体（应该继承全局LXGW WenKai）:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '我能吞下玻璃而不伤身体。这是一段测试文本，包含中文、English、数字123和符号！@#',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            
            const Text(
              '明确指定LXGW WenKai字体:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '我能吞下玻璃而不伤身体。这是一段测试文本，包含中文、English、数字123和符号！@#',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
              ),
            ),
            const SizedBox(height: 24),
            
            const Text(
              '不同字重的LXGW WenKai:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w300: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w300,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w400 (normal): 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w500: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w600: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w700 (bold): 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 24),
            
            const Text(
              '对比其他字体:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'LXGWNeoXiHei: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGWNeoXiHei',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FZYouSJK: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'FZYouSJK',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '系统默认字体: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: null, // 使用系统默认字体
              ),
            ),
          ],
        ),
      ),
    );
  }
}
