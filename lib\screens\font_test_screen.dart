import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class FontTestScreen extends StatelessWidget {
  const FontTestScreen({super.key});

  Future<List<String>> _getAvailableFonts() async {
    // 这是一个简化的字体检测方法
    // 在实际应用中，我们无法直接获取系统字体列表
    // 但我们可以检查我们配置的字体是否可用
    return [
      'LXGW WenKai',
      'LXGWNeoXiHei',
      'NeoXiHei Code',
      'FZYouSJK',
      '系统默认字体',
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('字体测试页面'),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '默认字体（应该继承全局LXG<PERSON> WenKai）:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '我能吞下玻璃而不伤身体。这是一段测试文本，包含中文、English、数字123和符号！@#',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 24),
            
            const Text(
              '明确指定LXGW WenKai字体:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              '我能吞下玻璃而不伤身体。这是一段测试文本，包含中文、English、数字123和符号！@#',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
              ),
            ),
            const SizedBox(height: 24),
            
            const Text(
              '不同字重的LXGW WenKai:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w300: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w300,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w400 (normal): 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w500: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w600: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FontWeight.w700 (bold): 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
                fontWeight: FontWeight.w700,
              ),
            ),
            const SizedBox(height: 24),
            
            const Text(
              '对比其他字体:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'LXGWNeoXiHei: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGWNeoXiHei',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FZYouSJK: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'FZYouSJK',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              '系统默认字体: 我能吞下玻璃而不伤身体',
              style: TextStyle(
                fontSize: 16,
                fontFamily: null, // 使用系统默认字体
              ),
            ),
            const SizedBox(height: 24),

            const Text(
              '字体加载调试信息:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            FutureBuilder<List<String>>(
              future: _getAvailableFonts(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const CircularProgressIndicator();
                }
                if (snapshot.hasError) {
                  return Text('错误: ${snapshot.error}');
                }
                final fonts = snapshot.data ?? [];
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('可用字体数量: ${fonts.length}'),
                    const SizedBox(height: 8),
                    ...fonts.take(10).map((font) => Text('- $font')),
                    if (fonts.length > 10) Text('... 还有 ${fonts.length - 10} 个字体'),
                  ],
                );
              },
            ),
            const SizedBox(height: 24),

            const Text(
              '特殊字符测试（用于区分字体）:',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            const Text(
              'LXGW WenKai: 霞鹜文楷 ≠ ≤ ≥ ∞ ∑ ∏ ∫ ∂ ∆ ∇ ∈ ∉ ∪ ∩ ⊂ ⊃',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGW WenKai',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'LXGWNeoXiHei: 霞鹜新晰黑 ≠ ≤ ≥ ∞ ∑ ∏ ∫ ∂ ∆ ∇ ∈ ∉ ∪ ∩ ⊂ ⊃',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'LXGWNeoXiHei',
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'FZYouSJK: 方正悠宋 ≠ ≤ ≥ ∞ ∑ ∏ ∫ ∂ ∆ ∇ ∈ ∉ ∪ ∩ ⊂ ⊃',
              style: TextStyle(
                fontSize: 16,
                fontFamily: 'FZYouSJK',
              ),
            ),
          ],
        ),
      ),
    );
  }
}
